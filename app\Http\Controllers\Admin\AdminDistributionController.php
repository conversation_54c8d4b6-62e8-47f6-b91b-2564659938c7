<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\TimePeriodFilter;
use App\Models\Distribution;
use App\Models\Product;
use App\Models\Store;
use App\Models\StockMovement;
use App\Services\ExcelExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminDistributionController extends Controller
{
    use TimePeriodFilter;
    /**
     * Display a listing of distributions
     */
    public function index(Request $request)
    {
        $query = Distribution::with(['store', 'product']);

        // Apply time period filter (replaces manual date filtering)
        $this->applyTimePeriodFilterDate($query, $request, 'date_distributed');

        // Get date range for form display
        $dateRange = $this->getDateRangeFromPeriod($request);
        $dateFrom = $dateRange['start'] ? $dateRange['start']->format('Y-m-d') : null;
        $dateTo = $dateRange['end'] ? $dateRange['end']->format('Y-m-d') : null;

        // Manual date override (if provided, override time period)
        if ($request->filled('date_from') || $request->filled('date_to')) {
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            if ($dateFrom) {
                $query->whereDate('date_distributed', '>=', $dateFrom);
            }
            if ($dateTo) {
                $query->whereDate('date_distributed', '<=', $dateTo);
            }
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('store', function ($sq) use ($search) {
                    $sq->where('name', 'like', "%{$search}%");
                })->orWhereHas('product', function ($pq) use ($search) {
                    $pq->where('name', 'like', "%{$search}%");
                });
            });
        }

        // Store filter
        if ($request->filled('store')) {
            $query->where('store_id', $request->get('store'));
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->get('status') === 'confirmed') {
                $query->where('confirmed', true);
            } elseif ($request->get('status') === 'unconfirmed') {
                $query->where('confirmed', false);
            }
        }

        // Sort by
        $sortBy = $request->get('sort_by', 'date_distributed');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['date_distributed', 'quantity', 'confirmed', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('date_distributed', 'desc');
        }

        $distributions = $query->paginate(10);

        // Get filter options
        $stores = Store::orderBy('name')->get();

        // Get period-specific statistics
        $stats = $this->getPeriodDistributionStats($request);

        return view('admin.distributions.index', compact('distributions', 'stores', 'stats', 'dateFrom', 'dateTo'));
    }

    /**
     * Show the form for creating a new distribution
     */
    public function create(Request $request)
    {
        // Get stores excluding "Gedung Pusat" (Central Warehouse)
        // Only get stores that have users assigned (actual store locations)
        $stores = Store::where('name', '!=', 'Gedung Pusat')
            ->whereHas('users') // Only stores with assigned users
            ->orderBy('name')
            ->get();

        $products = Product::orderBy('name')->get();

        // Pre-select store if provided
        $selectedStoreId = $request->get('store_id');

        return view('admin.distributions.create', compact('stores', 'products', 'selectedStoreId'));
    }

    /**
     * Store a newly created distribution in storage
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'store_id' => 'required|exists:stores,id',
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'date_distributed' => 'required|date',
        ], [
            'store_id.required' => 'Toko tujuan wajib dipilih',
            'store_id.exists' => 'Toko tujuan tidak valid',
            'product_id.required' => 'Produk wajib dipilih',
            'product_id.exists' => 'Produk tidak valid',
            'quantity.required' => 'Jumlah wajib diisi',
            'quantity.integer' => 'Jumlah harus berupa angka bulat',
            'quantity.min' => 'Jumlah minimal 1',
            'date_distributed.required' => 'Tanggal distribusi wajib diisi',
            'date_distributed.date' => 'Format tanggal tidak valid',
        ]);

        // Additional validation: Prevent distribution to "Gedung Pusat"
        $selectedStore = Store::find($validatedData['store_id']);
        if ($selectedStore && $selectedStore->name === 'Gedung Pusat') {
            return redirect()->back()
                ->withInput()
                ->withErrors([
                    'store_id' => 'Distribusi tidak dapat dibuat ke Gedung Pusat. Silakan pilih toko tujuan yang valid.'
                ]);
        }

        // Validate that the store has assigned users
        if ($selectedStore && !$selectedStore->users()->exists()) {
            return redirect()->back()
                ->withInput()
                ->withErrors([
                    'store_id' => 'Toko tujuan tidak memiliki pengguna yang terdaftar. Distribusi hanya dapat dibuat ke toko dengan pengguna aktif.'
                ]);
        }

        // Check warehouse stock availability using enhanced calculation
        $product = \App\Models\Product::findOrFail($validatedData['product_id']);
        $availableStock = $product->getAvailableStock();
        $currentStock = $product->getCurrentWarehouseStock();
        $committedStock = $product->getCommittedStock();

        if ($validatedData['quantity'] > $availableStock) {
            return redirect()->back()
                ->withInput()
                ->withErrors([
                    'quantity' => "Jumlah distribusi melebihi stok yang tersedia. Stok gudang: {$currentStock}, Sudah dialokasikan: {$committedStock}, Tersedia: {$availableStock}"
                ]);
        }

        // Create distribution with auto-acceptance and immediate stock movements
        DB::transaction(function () use ($validatedData, $product) {
            // Auto-confirm the distribution
            $validatedData['confirmed'] = true;
            $validatedData['received_quantity'] = $validatedData['quantity'];
            $validatedData['confirmed_at'] = now();

            $distribution = Distribution::create($validatedData);
            $store = Store::find($validatedData['store_id']);

            // Reduce warehouse stock immediately
            $warehouseStock = WarehouseStock::where('product_id', $product->id)
                ->whereNull('store_id')
                ->first();

            if ($warehouseStock) {
                $previousWarehouseStock = $warehouseStock->quantity;
                $newWarehouseStock = $previousWarehouseStock - $validatedData['quantity'];

                $warehouseStock->update(['quantity' => $newWarehouseStock]);

                // Log warehouse stock reduction
                StockMovement::create([
                    'product_id' => $product->id,
                    'type' => 'out',
                    'source' => 'distribution',
                    'quantity' => $validatedData['quantity'],
                    'previous_stock' => $previousWarehouseStock,
                    'new_stock' => $newWarehouseStock,
                    'reference_type' => 'Distribution',
                    'reference_id' => $distribution->id,
                    'notes' => "Distribusi auto-dikonfirmasi ke toko {$store->name}. Jumlah: {$validatedData['quantity']} unit",
                    'created_by' => auth()->id(),
                ]);
            }

            // Add to store stock immediately
            $existingStoreStock = \App\Models\StoreStock::where('store_id', $store->id)
                ->where('product_id', $product->id)
                ->first();

            if ($existingStoreStock) {
                $existingStoreStock->update([
                    'quantity' => $existingStoreStock->quantity + $validatedData['quantity'],
                ]);
            } else {
                \App\Models\StoreStock::create([
                    'store_id' => $store->id,
                    'product_id' => $product->id,
                    'quantity' => $validatedData['quantity'],
                ]);
            }
        });

        return redirect()->route('admin.distributions.index')
            ->with('success', 'Distribusi berhasil dibuat dan dikonfirmasi secara otomatis. Stok telah dipindahkan ke toko.');
    }

    /**
     * Display the specified distribution
     */
    public function show(Distribution $distribution)
    {
        $distribution->load(['store', 'product']);

        return view('admin.distributions.show', compact('distribution'));
    }

    /**
     * Show the form for editing the specified distribution
     */
    public function edit(Distribution $distribution)
    {
        // With auto-acceptance system, all distributions are confirmed and cannot be edited
        // This maintains data integrity and audit trails
        return redirect()->route('admin.distributions.index')
            ->with('error', 'Distribusi tidak dapat diedit karena sistem auto-konfirmasi telah memindahkan stok. Buat distribusi baru jika diperlukan.');

        // Get stores excluding "Gedung Pusat" (Central Warehouse)
        // Only get stores that have users assigned (actual store locations)
        $stores = Store::where('name', '!=', 'Gedung Pusat')
            ->whereHas('users') // Only stores with assigned users
            ->orderBy('name')
            ->get();

        $products = Product::orderBy('name')->get();

        return view('admin.distributions.edit', compact('distribution', 'stores', 'products'));
    }

    /**
     * Update the specified distribution in storage
     */
    public function update(Request $request, Distribution $distribution)
    {
        // With auto-acceptance system, all distributions are confirmed and cannot be updated
        // This maintains data integrity and audit trails
        return redirect()->route('admin.distributions.index')
            ->with('error', 'Distribusi tidak dapat diubah karena sistem auto-konfirmasi telah memindahkan stok. Buat distribusi baru jika diperlukan.');

        $validatedData = $request->validate([
            'store_id' => 'required|exists:stores,id',
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'date_distributed' => 'required|date',
        ]);

        // Additional validation: Prevent distribution to "Gedung Pusat"
        $selectedStore = Store::find($validatedData['store_id']);
        if ($selectedStore && $selectedStore->name === 'Gedung Pusat') {
            return redirect()->back()
                ->withInput()
                ->withErrors([
                    'store_id' => 'Distribusi tidak dapat dibuat ke Gedung Pusat. Silakan pilih toko tujuan yang valid.'
                ]);
        }

        // Validate that the store has assigned users
        if ($selectedStore && !$selectedStore->users()->exists()) {
            return redirect()->back()
                ->withInput()
                ->withErrors([
                    'store_id' => 'Toko tujuan tidak memiliki pengguna yang terdaftar. Distribusi hanya dapat dibuat ke toko dengan pengguna aktif.'
                ]);
        }

        // Check warehouse stock availability using enhanced calculation
        $product = \App\Models\Product::findOrFail($validatedData['product_id']);
        $currentStock = $product->getCurrentWarehouseStock();
        $committedStock = $product->getCommittedStock();

        // If updating, add back the current distribution quantity to available stock
        $currentDistributionQuantity = $distribution->quantity;
        $effectiveAvailableStock = $product->getAvailableStock() + $currentDistributionQuantity;

        if ($validatedData['quantity'] > $effectiveAvailableStock) {
            return redirect()->back()
                ->withInput()
                ->withErrors([
                    'quantity' => "Jumlah distribusi melebihi stok yang tersedia. Stok gudang: {$currentStock}, Sudah dialokasikan: " . ($committedStock - $currentDistributionQuantity) . ", Tersedia untuk edit: {$effectiveAvailableStock}"
                ]);
        }

        $distribution->update($validatedData);

        return redirect()->route('admin.distributions.index')
            ->with('success', 'Distribusi berhasil diperbarui');
    }

    /**
     * Remove the specified distribution from storage
     */
    public function destroy(Distribution $distribution)
    {
        // With auto-acceptance system, all distributions are confirmed and cannot be deleted
        // This maintains data integrity and audit trails
        return redirect()->route('admin.distributions.index')
            ->with('error', 'Distribusi tidak dapat dihapus karena sistem auto-konfirmasi telah memindahkan stok. Gunakan sistem retur jika diperlukan.');

        $distribution->delete();

        return redirect()->route('admin.distributions.index')
            ->with('success', 'Distribusi berhasil dihapus');
    }

    /**
     * Update distribution confirmation status with stock movement logging
     * WARNING: This method should be used carefully to avoid double stock deduction.
     * Normal distribution confirmations should be handled by store users via UserStockConfirmationController.
     */
    public function updateStatus(Request $request, Distribution $distribution)
    {
        $validatedData = $request->validate([
            'confirmed' => 'required|boolean',
        ]);

        DB::transaction(function () use ($distribution, $validatedData) {
            $wasConfirmed = $distribution->confirmed;
            $newConfirmed = $validatedData['confirmed'];

            // Log stock movement when status changes
            if (!$wasConfirmed && $newConfirmed) {
                // Distribution confirmed by admin - actual stock movement from warehouse
                // WARNING: This should only be used for administrative overrides
                // Check if this distribution was already processed by user confirmation
                $existingStockMovement = StockMovement::where('reference_type', 'Distribution')
                    ->where('reference_id', $distribution->id)
                    ->where('type', 'out')
                    ->where('source', 'distribution')
                    ->first();

                if ($existingStockMovement) {
                    \Log::warning('AdminDistributionController: Distribution already has stock movement, skipping warehouse stock reduction', [
                        'distribution_id' => $distribution->id,
                        'existing_movement_id' => $existingStockMovement->id
                    ]);

                    // Just update the distribution status without touching warehouse stock
                    $distribution->update([
                        'confirmed' => $newConfirmed,
                        'received_quantity' => $distribution->received_quantity ?? $distribution->quantity,
                        'confirmed_at' => $distribution->confirmed_at ?? now(),
                        'notes' => ($distribution->notes ?? '') . ' [Status diperbarui oleh admin]'
                    ]);
                } else {
                    $product = $distribution->product;
                    $warehouseStock = \App\Models\WarehouseStock::where('product_id', $product->id)
                        ->whereNull('store_id') // Only central warehouse stock
                        ->first();

                    if ($warehouseStock) {
                        $previousStock = $warehouseStock->quantity;
                        $newStock = $previousStock - $distribution->quantity;

                        \Log::info('AdminDistributionController: Reducing warehouse stock for admin confirmation', [
                            'distribution_id' => $distribution->id,
                            'product_id' => $product->id,
                            'previous_stock' => $previousStock,
                            'reduction' => $distribution->quantity,
                            'new_stock' => $newStock
                        ]);

                        // Update warehouse stock
                        $warehouseStock->update(['quantity' => $newStock]);

                        // Update distribution with admin confirmation details
                        $distribution->update([
                            'confirmed' => $newConfirmed,
                            'received_quantity' => $distribution->quantity, // Assume full quantity received
                            'confirmed_at' => now(),
                            'notes' => ($distribution->notes ?? '') . ' [Dikonfirmasi oleh admin]'
                        ]);

                        // Log the actual stock movement (use positive quantity for consistency)
                        StockMovement::create([
                            'product_id' => $product->id,
                            'type' => 'out',
                            'source' => 'distribution',
                            'quantity' => $distribution->quantity, // Positive quantity for outbound movement
                            'previous_stock' => $previousStock,
                            'new_stock' => $newStock,
                            'reference_type' => 'Distribution',
                            'reference_id' => $distribution->id,
                            'notes' => "Konfirmasi distribusi oleh admin ke " . $distribution->store->name . " - stok keluar dari gudang",
                            'created_by' => auth()->id(),
                        ]);
                    }
                }
            } elseif ($wasConfirmed && !$newConfirmed) {
                // Distribution unconfirmed - return stock to warehouse
                $product = $distribution->product;
                $warehouseStock = \App\Models\WarehouseStock::where('product_id', $product->id)
                    ->whereNull('store_id') // Only central warehouse stock
                    ->first();

                if ($warehouseStock) {
                    $previousStock = $warehouseStock->quantity;
                    $newStock = $previousStock + $distribution->quantity;

                    // Update warehouse stock
                    $warehouseStock->update(['quantity' => $newStock]);

                    // Update distribution status
                    $distribution->update([
                        'confirmed' => $newConfirmed,
                        'confirmed_at' => null,
                        'received_quantity' => null,
                    ]);

                    // Log the stock return
                    StockMovement::create([
                        'product_id' => $product->id,
                        'type' => 'in',
                        'source' => 'adjustment',
                        'quantity' => $distribution->quantity,
                        'previous_stock' => $previousStock,
                        'new_stock' => $newStock,
                        'reference_type' => 'Distribution',
                        'reference_id' => $distribution->id,
                        'notes' => "Pembatalan konfirmasi distribusi ke " . $distribution->store->name . " - stok kembali ke gudang",
                        'created_by' => auth()->id(),
                    ]);
                }
            } else {
                // Just update the status without stock changes
                $distribution->update([
                    'confirmed' => $newConfirmed,
                ]);
            }
        });

        $statusText = $validatedData['confirmed'] ? 'Dikonfirmasi' : 'Belum Dikonfirmasi';

        return redirect()->route('admin.distributions.show', $distribution)
            ->with('success', "Status distribusi berhasil diubah menjadi: {$statusText}");
    }

    /**
     * Get available warehouse stock for a product (AJAX endpoint)
     */
    public function getWarehouseStock(Request $request, $productId)
    {
        $product = \App\Models\Product::findOrFail($productId);

        $currentStock = $product->getCurrentWarehouseStock();
        $committedStock = 0; // With auto-acceptance, no committed stock exists
        $availableStock = $currentStock; // All current stock is available
        $pendingDistributionsCount = 0; // With auto-acceptance, no pending distributions exist

        return response()->json([
            'available_stock' => $availableStock,
            'current_stock' => $currentStock,
            'committed_stock' => $committedStock,
            'pending_distributions_count' => $pendingDistributionsCount
        ]);
    }

    /**
     * Export distributions data to Excel
     */
    public function export(Request $request, ExcelExportService $excelService)
    {
        // Get user's timezone
        $userTimezone = auth()->user()->timezone ?? 'Asia/Jakarta';

        // Apply same filters as index method
        $query = Distribution::with(['store', 'product']);

        // Apply time period filter
        $this->applyTimePeriodFilterDate($query, $request, 'date_distributed');

        // Get date range for form display
        $dateRange = $this->getDateRangeFromPeriod($request);
        $dateFrom = $dateRange['start'] ? $dateRange['start']->format('Y-m-d') : null;
        $dateTo = $dateRange['end'] ? $dateRange['end']->format('Y-m-d') : null;

        // Manual date override (if provided, override time period)
        if ($request->filled('date_from') || $request->filled('date_to')) {
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            if ($dateFrom) {
                $query->whereDate('date_distributed', '>=', $dateFrom);
            }
            if ($dateTo) {
                $query->whereDate('date_distributed', '<=', $dateTo);
            }
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->whereHas('store', function ($sq) use ($search) {
                    $sq->where('name', 'like', "%{$search}%");
                })->orWhereHas('product', function ($pq) use ($search) {
                    $pq->where('name', 'like', "%{$search}%");
                });
            });
        }

        // Store filter
        if ($request->filled('store')) {
            $query->where('store_id', $request->get('store'));
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->get('status') === 'confirmed') {
                $query->where('confirmed', true);
            } elseif ($request->get('status') === 'unconfirmed') {
                $query->where('confirmed', false);
            }
        }

        // Sort by
        $sortBy = $request->get('sort_by', 'date_distributed');
        $sortOrder = $request->get('sort_order', 'desc');

        if (in_array($sortBy, ['date_distributed', 'quantity', 'confirmed', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('date_distributed', 'desc');
        }

        $distributions = $query->get();

        // Prepare worksheets data
        $worksheets = [];

        // Distributions worksheet
        $distributionData = [];
        $no = 1;
        foreach ($distributions as $distribution) {
            $dateDistributed = $excelService->formatDateOnly($distribution->date_distributed, $userTimezone);
            $productName = $distribution->product ? $distribution->product->name : 'N/A';
            $storeName = $distribution->store ? $distribution->store->name : 'N/A';
            $storeLocation = $distribution->store ? $distribution->store->location : 'N/A';
            $status = $distribution->confirmed ? 'Dikonfirmasi' : 'Belum Dikonfirmasi';
            $notes = $distribution->notes ?? '';
            $createdAt = $excelService->formatDate($distribution->created_at, $userTimezone);

            $distributionData[] = [
                $no++,
                $dateDistributed,
                $productName,
                $storeName,
                $storeLocation,
                $distribution->quantity,
                $distribution->received_quantity ?? 0,
                $status,
                $notes,
                $createdAt
            ];
        }

        $subtitle = '';
        if ($dateFrom && $dateTo) {
            $subtitle = 'Periode: ' . \Carbon\Carbon::parse($dateFrom)->format('d/m/Y') . ' - ' . \Carbon\Carbon::parse($dateTo)->format('d/m/Y');
        }

        $worksheets[] = [
            'name' => 'Data Distribusi',
            'title' => 'DATA DISTRIBUSI - INDAH BERKAH ABADI',
            'subtitle' => $subtitle,
            'headers' => ['No', 'Tanggal Distribusi', 'Produk', 'Toko', 'Lokasi', 'Jumlah', 'Jumlah Diterima', 'Status', 'Catatan', 'Dibuat Pada'],
            'data' => $distributionData
        ];

        // Create filename
        $startDate = $dateFrom ? \Carbon\Carbon::parse($dateFrom) : null;
        $endDate = $dateTo ? \Carbon\Carbon::parse($dateTo) : null;
        $filename = $excelService->createFilename('data_distribusi', $startDate, $endDate);

        return $excelService->generateExcelFile($worksheets, $filename, $userTimezone);
    }
}
