@extends('layouts.user')

@section('title', 'Detail Distribusi - Indah Berkah Abadi')

@section('content')
<div class="user-dashboard-container">
    <!-- Header -->
    <div class="user-dashboard-header">
        <div class="user-dashboard-header-content">
            <h1 class="user-dashboard-header-title">Detail Distribusi</h1>
            <p class="user-dashboard-header-subtitle">Informasi lengkap distribusi dari gudang</p>
        </div>
        <div class="user-dashboard-header-actions">
            <a href="{{ route('user.deliveries') }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-4 py-2 text-sm flex items-center user-dashboard-btn-sm">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                </svg>
                Kembali
            </a>

        </div>
    </div>

    <!-- Distribution Status -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-content">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-lg font-semibold text-gray-900">Status Distribusi</h2>
                    <p class="text-sm text-gray-600 mt-1">Status saat ini dari distribusi ini</p>
                </div>
                <span class="inline-flex items-center px-4 py-2 rounded-full text-lg font-semibold
                    {{ $delivery->confirmed 
                        ? 'bg-green-100 text-green-800 border border-green-200 shadow-sm' 
                        : 'bg-yellow-100 text-yellow-800 border border-yellow-200 shadow-sm' }}">
                    @if($delivery->confirmed)
                        <svg class="w-5 h-5 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path>
                        </svg>
                        Dikonfirmasi
                    @else
                        <svg class="w-5 h-5 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3"></path>
                        </svg>
                        Menunggu Konfirmasi
                    @endif
                </span>
            </div>
        </div>
    </div>

    <!-- Distribution Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Product Information -->
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-header">
                <h2 class="user-dashboard-card-title">Informasi Produk</h2>
            </div>
            <div class="user-dashboard-card-content">
                <div class="space-y-4">
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Nama Produk</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">
                            {{ $delivery->product ? $delivery->product->name : 'Produk tidak diketahui' }}
                        </p>
                    </div>
                    @if($delivery->confirmed && $delivery->received_quantity !== null)
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Jumlah Diterima</label>
                        <p class="text-lg font-semibold text-green-600 mt-1">
                            {{ number_format($delivery->received_quantity) }} unit
                        </p>
                        @if($delivery->received_quantity != $delivery->quantity)
                        <p class="text-sm text-gray-500 mt-1">
                            Dari {{ number_format($delivery->quantity) }} unit yang didistribusikan
                        </p>
                        @endif
                    </div>
                    @else
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Jumlah Distribusi</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">
                            {{ number_format($delivery->quantity) }} unit
                        </p>
                        <p class="text-sm text-gray-500 mt-1">
                            Belum dikonfirmasi
                        </p>
                    </div>
                    @endif
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Tanggal Distribusi</label>
                        <p class="text-gray-900 mt-1">
                            {{ $delivery->date_distributed ? $delivery->date_distributed->format('d M Y') : 'Belum dijadwalkan' }}
                        </p>
                    </div>
                    @if($delivery->confirmed && $delivery->notes)
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Catatan Penerimaan</label>
                        <div class="bg-gray-50 rounded-lg p-3 mt-1">
                            <p class="text-gray-900 text-sm">{{ $delivery->notes }}</p>
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Store Information -->
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-header">
                <h2 class="user-dashboard-card-title">Informasi Toko</h2>
            </div>
            <div class="user-dashboard-card-content">
                <div class="space-y-4">
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Nama Toko</label>
                        <p class="text-lg font-semibold text-gray-900 mt-1">
                            {{ $delivery->store ? $delivery->store->name : 'Toko tidak diketahui' }}
                        </p>
                    </div>
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Lokasi</label>
                        <p class="text-gray-900 mt-1">
                            {{ $delivery->store ? $delivery->store->location : 'Lokasi tidak diketahui' }}
                        </p>
                    </div>
                    <div class="user-dashboard-form-group">
                        <label class="user-dashboard-form-label">Tanggal Dibuat</label>
                        <p class="text-gray-900 mt-1">
                            {{ $delivery->created_at ? $delivery->created_at->format('d M Y, H:i') : 'Tidak diketahui' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Timeline -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Timeline Distribusi</h2>
            <p class="user-dashboard-card-description">Riwayat status distribusi</p>
        </div>
        <div class="user-dashboard-card-content">
            <div class="space-y-4">
                <div class="user-dashboard-form-group">
                    <ul class="space-y-6">
                        <li>
                            <div class="flex items-start space-x-3">
                                <span class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center ring-8 ring-white">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                </span>
                                <div>
                                    <p class="text-sm text-gray-500">
                                        Distribusi dibuat pada 
                                        <time datetime="{{ $delivery->created_at ? $delivery->created_at->toISOString() : '' }}">
                                            {{ $delivery->created_at ? $delivery->created_at->format('d M Y, H:i') : 'Tidak diketahui' }}
                                        </time>
                                    </p>
                                </div>
                            </div>
                        </li>
                        @if($delivery->date_distributed)
                        <li>
                            <div class="flex items-start space-x-3">
                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                                    </svg>
                                </span>
                                <div>
                                    <p class="text-sm text-gray-500">
                                        Distribusi dijadwalkan pada 
                                        <time datetime="{{ $delivery->date_distributed->toISOString() }}">
                                            {{ $delivery->date_distributed->format('d M Y') }}
                                        </time>
                                    </p>
                                </div>
                            </div>
                        </li>
                        @endif
                        @if($delivery->confirmed)
                        <li>
                            <div class="flex items-start space-x-3">
                                <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </span>
                                <div>
                                    <p class="text-sm text-gray-500">
                                        Distribusi dikonfirmasi pada
                                        <time datetime="{{ $delivery->confirmed_at ? $delivery->confirmed_at->toISOString() : '' }}">
                                            {{ $delivery->confirmed_at ? $delivery->confirmed_at->format('d M Y, H:i') : 'Tidak diketahui' }}
                                        </time>
                                    </p>
                                    @if($delivery->received_quantity !== null && $delivery->received_quantity != $delivery->quantity)
                                    <p class="text-xs text-gray-400 mt-1">
                                        Diterima {{ number_format($delivery->received_quantity) }} dari {{ number_format($delivery->quantity) }} unit
                                    </p>
                                    @endif
                                </div>
                            </div>
                        </li>
                        @else
                        <li>
                            <div class="flex items-start space-x-3">
                                <span class="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center ring-8 ring-white">
                                    <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </span>
                                <div>
                                    <p class="text-sm text-gray-500">Menunggu konfirmasi penerimaan</p>
                                </div>
                            </div>
                        </li>
                        @endif
                    </ul>
                </div>
            </div>
        </div>
    </div>



    @if($delivery->confirmed && $delivery->max_returnable_quantity > 0)
    <!-- Return Action Card -->
    <div class="user-dashboard-card bg-orange-50 border-orange-200">
        <div class="user-dashboard-card-content">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-orange-900">Buat Permintaan Retur</h3>
                    <p class="text-sm text-orange-700 mt-1">
                        Tersedia {{ $delivery->max_returnable_quantity }} unit yang dapat diretur dari distribusi ini.
                    </p>
                </div>
                <button type="button"
                        class="bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg px-4 py-2 text-sm flex items-center"
                        onclick="openReturnModal('{{ $delivery->id }}', '{{ $delivery->product->name }}', {{ $delivery->max_returnable_quantity }}, '{{ $delivery->product->id }}')">
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3"></path>
                    </svg>
                    Buat Retur
                </button>
            </div>
        </div>
    </div>
    @endif
</div>



<!-- Return Modal -->
<div id="returnModal" class="user-dashboard-deliveries-modal">
    <div class="user-dashboard-deliveries-modal-content">
        <div class="user-dashboard-deliveries-modal-header">
            <h3 class="user-dashboard-deliveries-modal-title">Buat Permintaan Retur</h3>
            <button type="button" onclick="closeReturnModal()" class="user-dashboard-deliveries-modal-close">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="returnForm" method="POST" action="{{ route('user.returns.store-from-distribution') }}">
            @csrf
            <input type="hidden" id="distribution_id" name="distribution_id" value="">
            <input type="hidden" id="product_id" name="product_id" value="">

            <div class="user-dashboard-deliveries-modal-body">
                <div class="user-dashboard-deliveries-form-group">
                    <div class="user-dashboard-deliveries-info-box">
                        <div class="user-dashboard-deliveries-info-content">
                            <svg class="user-dashboard-deliveries-info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="user-dashboard-deliveries-info-text">Buat retur untuk produk <strong id="returnProductName"></strong> yang memiliki masalah atau tidak sesuai.</p>
                        </div>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_quantity" class="user-dashboard-deliveries-form-label">
                            Jumlah Retur (Maksimal: <span id="maxReturnQuantity"></span>)
                        </label>
                        <input type="number"
                               id="return_quantity"
                               name="quantity"
                               class="user-dashboard-deliveries-form-input"
                               min="1"
                               value="1"
                               required>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_reason" class="user-dashboard-deliveries-form-label">
                            Alasan Retur <span class="user-dashboard-deliveries-form-required">*</span>
                        </label>
                        <select id="return_reason" name="reason" class="user-dashboard-deliveries-form-select" required>
                            <option value="">Pilih Alasan</option>
                            <option value="damaged">Rusak</option>
                            <option value="expired">Kadaluarsa</option>
                            <option value="defective">Cacat</option>
                            <option value="shortage">Kekurangan Pengiriman</option>
                            <option value="other">Lainnya</option>
                        </select>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_description" class="user-dashboard-deliveries-form-label">
                            Deskripsi Detail <span class="user-dashboard-deliveries-form-required">*</span>
                        </label>
                        <textarea id="return_description"
                                  name="description"
                                  rows="3"
                                  class="user-dashboard-deliveries-form-textarea"
                                  placeholder="Jelaskan detail masalah atau alasan retur..."
                                  required></textarea>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_date" class="user-dashboard-deliveries-form-label">
                            Tanggal Retur <span class="user-dashboard-deliveries-form-required">*</span>
                        </label>
                        <input type="date"
                               id="return_date"
                               name="return_date"
                               class="user-dashboard-deliveries-form-input"
                               max="{{ date('Y-m-d') }}"
                               value="{{ date('Y-m-d') }}"
                               required>
                    </div>
                </div>
            </div>

            <div class="user-dashboard-deliveries-modal-footer">
                <button type="button" onclick="closeReturnModal()" class="user-dashboard-deliveries-btn user-dashboard-deliveries-btn-outline">
                    Batal
                </button>
                <button type="submit" class="user-dashboard-deliveries-btn user-dashboard-deliveries-btn-primary">
                    Buat Retur
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<script src="{{ asset('js/user-dashboard-deliveries.js') }}"></script>
@endpush

@endsection
