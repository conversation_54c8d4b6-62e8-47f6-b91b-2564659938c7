<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\SupplierDelivery;
use App\Models\ReturnModel;
use App\Models\WarehouseStock;
use App\Models\StoreStock;
use App\Models\StockMovement;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class SupplierReturnProcessingTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $supplier;
    protected $supplierUser;
    protected $product;
    protected $supplierDelivery;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'email' => '<EMAIL>',
        ]);

        // Create supplier
        $this->supplier = Supplier::factory()->create([
            'name' => 'Test Supplier',
            'status' => 'active',
        ]);

        // Create supplier user
        $this->supplierUser = User::factory()->create([
            'role' => 'supplier',
            'supplier_id' => $this->supplier->id,
            'email' => '<EMAIL>',
        ]);

        // Create product
        $this->product = Product::factory()->create([
            'name' => 'Test Product',
            'sku' => 'TEST-001',
        ]);

        // Create supplier delivery
        $this->supplierDelivery = SupplierDelivery::factory()->create([
            'supplier_id' => $this->supplier->id,
            'product_id' => $this->product->id,
            'quantity' => 100,
            'received_quantity' => 90, // 10 units short
            'status' => 'partial',
            'delivery_date' => now(),
            'received_date' => now(),
            'received_by' => $this->admin->id,
        ]);
    }

    /** @test */
    public function it_calculates_max_returnable_quantity_correctly_before_any_returns()
    {
        $this->actingAs($this->admin);

        $response = $this->get(route('admin.supplier-deliveries.show', $this->supplierDelivery));

        $response->assertStatus(200);
        
        // Max returnable should be: sent_quantity (100) - received_quantity (90) = 10
        $delivery = $response->viewData('delivery');
        $this->assertEquals(10, $delivery->max_returnable_quantity);
    }

    /** @test */
    public function it_reduces_max_returnable_quantity_after_supplier_accepts_return()
    {
        // Create a warehouse-to-supplier return
        $return = ReturnModel::create([
            'supplier_delivery_id' => $this->supplierDelivery->id,
            'product_id' => $this->product->id,
            'supplier_id' => $this->supplier->id,
            'quantity' => 5,
            'reason' => 'damaged',
            'description' => 'Test return',
            'status' => 'approved',
            'return_date' => now(),
            'approved_date' => now(),
            'requested_by' => $this->admin->id,
            'approved_by' => $this->admin->id,
        ]);

        // Supplier accepts the return (status changes to in_transit)
        $this->actingAs($this->supplierUser);
        $response = $this->post(route('supplier.returns.respond', $return), [
            'action' => 'accept',
            'supplier_notes' => 'Return accepted',
        ]);

        $response->assertRedirect(route('supplier.returns.index'));
        
        // Verify return status changed to in_transit
        $return->refresh();
        $this->assertEquals('in_transit', $return->status);

        // Check max returnable quantity is reduced
        $this->actingAs($this->admin);
        $response = $this->get(route('admin.supplier-deliveries.show', $this->supplierDelivery));
        
        $delivery = $response->viewData('delivery');
        // Max returnable should now be: sent_quantity (100) - received_quantity (90) - accepted_returns (5) = 5
        $this->assertEquals(5, $delivery->max_returnable_quantity);
    }

    /** @test */
    public function it_accounts_for_processed_returns_with_accepted_action()
    {
        // Create a return and move it to history with accepted action
        $return = ReturnModel::create([
            'supplier_delivery_id' => $this->supplierDelivery->id,
            'product_id' => $this->product->id,
            'supplier_id' => $this->supplier->id,
            'quantity' => 8,
            'reason' => 'defective',
            'description' => 'Test processed return',
            'status' => 'completed',
            'return_date' => now(),
            'approved_date' => now(),
            'completed_date' => now(),
            'requested_by' => $this->admin->id,
            'approved_by' => $this->admin->id,
        ]);

        // Move to history with accepted action
        $return->moveToHistory('accepted', 'Return processed and accepted', $this->admin->id);

        // Check max returnable quantity accounts for processed return
        $this->actingAs($this->admin);
        $response = $this->get(route('admin.supplier-deliveries.show', $this->supplierDelivery));
        
        $delivery = $response->viewData('delivery');
        // Max returnable should be: sent_quantity (100) - received_quantity (90) - processed_accepted_returns (8) = 2
        $this->assertEquals(2, $delivery->max_returnable_quantity);
    }

    /** @test */
    public function it_prevents_creating_return_exceeding_max_returnable_quantity()
    {
        // Create a return that uses up most of the returnable quantity
        ReturnModel::create([
            'supplier_delivery_id' => $this->supplierDelivery->id,
            'product_id' => $this->product->id,
            'supplier_id' => $this->supplier->id,
            'quantity' => 8,
            'reason' => 'damaged',
            'description' => 'First return',
            'status' => 'in_transit', // Accepted by supplier
            'return_date' => now(),
            'approved_date' => now(),
            'requested_by' => $this->admin->id,
            'approved_by' => $this->admin->id,
        ]);

        // Try to create another return that would exceed max returnable quantity
        $this->actingAs($this->admin);
        $response = $this->post(route('admin.returns.store'), [
            'product_id' => $this->product->id,
            'supplier_id' => $this->supplier->id,
            'supplier_delivery_id' => $this->supplierDelivery->id,
            'quantity' => 5, // This would exceed the remaining 2 units
            'reason' => 'defective',
            'description' => 'Second return - should fail',
            'return_date' => now()->format('Y-m-d'),
        ]);

        $response->assertRedirect();
        $response->assertSessionHasErrors(['quantity']);
        
        // Verify the error message mentions the correct max returnable quantity
        $errors = session('errors');
        $this->assertStringContains('tidak boleh melebihi 2 unit', $errors->first('quantity'));
    }

    /** @test */
    public function it_reduces_warehouse_stock_when_supplier_approves_warehouse_return()
    {
        // Create initial warehouse stock
        $initialWarehouseStock = 50;
        WarehouseStock::create([
            'product_id' => $this->product->id,
            'store_id' => null, // Central warehouse
            'quantity' => $initialWarehouseStock,
        ]);

        // Create a warehouse-to-supplier return (no store_id)
        $returnQuantity = 15;
        $warehouseReturn = ReturnModel::create([
            'product_id' => $this->product->id,
            'supplier_id' => $this->supplier->id,
            'store_id' => null, // This makes it a warehouse return
            'quantity' => $returnQuantity,
            'reason' => 'defective',
            'description' => 'Warehouse return test',
            'status' => 'requested',
            'return_date' => now(),
            'requested_by' => $this->admin->id,
        ]);

        // Verify initial warehouse stock
        $warehouseStock = WarehouseStock::where('product_id', $this->product->id)
            ->whereNull('store_id')
            ->first();
        $this->assertEquals($initialWarehouseStock, $warehouseStock->quantity);

        // Supplier approves the return
        $this->actingAs($this->supplierUser);
        $response = $this->put(route('supplier.returns.approve', $warehouseReturn), [
            'notes' => 'Warehouse return approved by supplier',
        ]);

        $response->assertRedirect(route('supplier.returns.index'));
        $response->assertSessionHas('success');

        // Verify warehouse stock was reduced
        $warehouseStock->refresh();
        $expectedStock = $initialWarehouseStock - $returnQuantity;
        $this->assertEquals($expectedStock, $warehouseStock->quantity);

        // Verify stock movement was recorded
        $stockMovement = StockMovement::where('product_id', $this->product->id)
            ->where('source', 'return_to_supplier')
            ->where('reference_type', 'ReturnModel')
            ->where('reference_id', $warehouseReturn->id)
            ->first();

        $this->assertNotNull($stockMovement);
        $this->assertEquals('out', $stockMovement->type);
        $this->assertEquals(-$returnQuantity, $stockMovement->quantity);
        $this->assertEquals($initialWarehouseStock, $stockMovement->previous_stock);
        $this->assertEquals($expectedStock, $stockMovement->new_stock);
        $this->assertStringContains('Retur dari gudang disetujui oleh supplier', $stockMovement->notes);

        // Verify return was moved to history
        $warehouseReturn->refresh();
        $this->assertEquals('completed', $warehouseReturn->status);
        $this->assertFalse($warehouseReturn->is_active);
        $this->assertEquals('processed', $warehouseReturn->status);
    }

    /** @test */
    public function it_reduces_store_stock_when_supplier_approves_store_return()
    {
        // Create a store for testing
        $store = \App\Models\Store::factory()->create([
            'name' => 'Test Store',
            'address' => 'Test Address',
            'timezone' => 'WIB',
        ]);

        // Create initial store stock
        $initialStoreStock = 30;
        StoreStock::create([
            'store_id' => $store->id,
            'product_id' => $this->product->id,
            'quantity' => $initialStoreStock,
        ]);

        // Create a store-to-supplier return
        $returnQuantity = 8;
        $storeReturn = ReturnModel::create([
            'product_id' => $this->product->id,
            'supplier_id' => $this->supplier->id,
            'store_id' => $store->id, // This makes it a store return
            'quantity' => $returnQuantity,
            'reason' => 'damaged',
            'description' => 'Store return test',
            'status' => 'requested',
            'return_date' => now(),
            'requested_by' => $this->admin->id,
        ]);

        // Verify initial store stock
        $storeStock = StoreStock::where('store_id', $store->id)
            ->where('product_id', $this->product->id)
            ->first();
        $this->assertEquals($initialStoreStock, $storeStock->quantity);

        // Supplier approves the return
        $this->actingAs($this->supplierUser);
        $response = $this->put(route('supplier.returns.approve', $storeReturn), [
            'notes' => 'Store return approved by supplier',
        ]);

        $response->assertRedirect(route('supplier.returns.index'));
        $response->assertSessionHas('success');

        // Verify store stock was reduced
        $storeStock->refresh();
        $expectedStock = $initialStoreStock - $returnQuantity;
        $this->assertEquals($expectedStock, $storeStock->quantity);

        // Verify stock movement was recorded
        $stockMovement = StockMovement::where('product_id', $this->product->id)
            ->where('source', 'return_to_supplier')
            ->where('reference_type', 'ReturnModel')
            ->where('reference_id', $storeReturn->id)
            ->first();

        $this->assertNotNull($stockMovement);
        $this->assertEquals('out', $stockMovement->type);
        $this->assertEquals(-$returnQuantity, $stockMovement->quantity);
        $this->assertEquals($initialStoreStock, $stockMovement->previous_stock);
        $this->assertEquals($expectedStock, $stockMovement->new_stock);
        $this->assertStringContains('Retur dari toko disetujui oleh supplier', $stockMovement->notes);

        // Verify return was moved to history
        $storeReturn->refresh();
        $this->assertEquals('completed', $storeReturn->status);
        $this->assertFalse($storeReturn->is_active);
        $this->assertEquals('processed', $storeReturn->status);
    }
}
