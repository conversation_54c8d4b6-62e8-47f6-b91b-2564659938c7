<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Stores table
        Schema::create('stores', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('location');
            $table->timestamps();
        });

        // Products table (simplified)
        Schema::create('products', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->timestamps();
        });

        // Warehouse Stock table
        Schema::create('warehouse_stock', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('product_id')->constrained('products')->onDelete('cascade');
            $table->integer('quantity');
            $table->foreignUuid('store_id')->nullable()->constrained('stores')->onDelete('cascade');
            $table->date('date_received');
            $table->timestamps();
        });

        // Distributions table
        Schema::create('distributions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignUuid('store_id')->constrained('stores')->onDelete('cascade');
            $table->integer('quantity');
            $table->integer('received_quantity')->nullable(); // Auto-set to quantity on creation
            $table->text('notes')->nullable(); // Notes about the distribution/receipt
            $table->date('date_distributed');
            $table->boolean('confirmed')->default(true); // Auto-confirmed in simplified system
            $table->timestamp('confirmed_at')->nullable(); // Auto-set on creation
            $table->timestamps();
        });

        // Store Stock table
        Schema::create('store_stock', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('store_id')->constrained('stores')->onDelete('cascade');
            $table->foreignUuid('product_id')->constrained('products')->onDelete('cascade');
            $table->integer('quantity');
            $table->timestamps();
        });

        // Stock Opname table
        Schema::create('stock_opname', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('store_id')->constrained('stores')->onDelete('cascade');
            $table->foreignUuid('product_id')->constrained('products')->onDelete('cascade');
            $table->integer('system_quantity');
            $table->integer('physical_quantity');
            $table->date('date');
            $table->timestamps();
        });

        // Suppliers table
        Schema::create('suppliers', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('contact_person')->nullable();
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->text('address')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
        });

        // Supplier Deliveries table (tracking deliveries from suppliers to warehouse)
        Schema::create('supplier_deliveries', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('supplier_id')->constrained('suppliers')->onDelete('cascade');
            $table->foreignUuid('product_id')->constrained('products')->onDelete('cascade');
            $table->integer('quantity');
            $table->integer('received_quantity')->nullable(); // Auto-set to quantity on creation
            $table->decimal('unit_price', 10, 2)->nullable(); // Price per unit
            $table->decimal('total_price', 12, 2)->nullable(); // Total price for delivery
            $table->date('delivery_date');
            $table->date('received_date')->nullable(); // Auto-set on creation
            $table->enum('status', ['pending', 'received', 'partial', 'cancelled'])->default('received'); // Auto-received in simplified system
            $table->text('notes')->nullable();
            $table->foreignUuid('received_by')->nullable()->constrained('users')->onDelete('set null'); // Auto-set to creator
            $table->timestamps();
        });

        // Returns table (tracking damaged product returns: Store→Warehouse→Supplier)
        Schema::create('returns', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('product_id')->constrained('products')->onDelete('cascade');
            $table->foreignUuid('store_id')->nullable()->constrained('stores')->onDelete('cascade'); // Null if return from warehouse
            $table->foreignUuid('supplier_id')->nullable()->constrained('suppliers')->onDelete('cascade'); // Null if not returned to supplier
            $table->integer('quantity');
            $table->enum('reason', ['damaged', 'expired', 'defective', 'overstock', 'shortage', 'other'])->default('damaged');
            $table->text('description')->nullable(); // Detailed description of the issue
            $table->enum('status', ['requested', 'approved', 'in_transit', 'completed', 'rejected', 'processed'])->default('requested');
            $table->date('return_date');
            $table->date('approved_date')->nullable();
            $table->date('completed_date')->nullable();
            $table->date('processed_date')->nullable(); // When return was processed (moved to history)
            $table->enum('processing_action', ['deleted', 'resent', 'accepted', 'rejected_final'])->nullable(); // Action taken when processed
            $table->foreignUuid('requested_by')->constrained('users')->onDelete('cascade'); // User who requested the return
            $table->foreignUuid('approved_by')->nullable()->constrained('users')->onDelete('set null'); // Admin who approved
            $table->foreignUuid('processed_by')->nullable()->constrained('users')->onDelete('set null'); // Who processed the return
            $table->text('admin_notes')->nullable(); // Notes from admin
            $table->text('processing_notes')->nullable(); // Notes when processing the return
            $table->boolean('is_active')->default(true); // False when moved to history
            $table->timestamps();

            // Indexes for efficient querying
            $table->index(['is_active', 'store_id']); // For active store returns
            $table->index(['is_active', 'supplier_id']); // For active supplier returns
            $table->index(['processed_date', 'processing_action']); // For history queries
            $table->index(['return_date', 'is_active']); // For date-based filtering
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('returns');
        Schema::dropIfExists('supplier_deliveries');
        Schema::dropIfExists('suppliers');
        Schema::dropIfExists('stock_opname');
        Schema::dropIfExists('store_stock');
        Schema::dropIfExists('distributions');
        Schema::dropIfExists('warehouse_stock');
        Schema::dropIfExists('products');
        Schema::dropIfExists('stores');
    }
};
