<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\TimePeriodFilter;
use App\Models\Distribution;
use App\Models\DistributionItem;
use App\Models\WarehouseStock;
use App\Models\StockMovement;
use Illuminate\Http\Request;

class UserDeliveryController extends Controller
{
    use TimePeriodFilter;
    /**
     * Display a listing of deliveries for the user's store
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $store = $user->store;

        if (!$store) {
            return redirect()->route('user.dashboard')->with('error', 'Anda belum terdaftar di toko manapun.');
        }

        $query = Distribution::where('store_id', $store->id)
            ->with(['product', 'store']);

        // Apply time period filter
        $this->applyTimePeriodFilterDate($query, $request, 'date_distributed');

        // Get date range for form display
        $dateRange = $this->getDateRangeFromPeriod($request);
        $dateFrom = $dateRange['start'] ? $dateRange['start']->format('Y-m-d') : null;
        $dateTo = $dateRange['end'] ? $dateRange['end']->format('Y-m-d') : null;

        // Manual date override (if provided, override time period)
        if ($request->filled('date_from') || $request->filled('date_to')) {
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            if ($dateFrom) {
                $query->whereDate('date_distributed', '>=', $dateFrom);
            }
            if ($dateTo) {
                $query->whereDate('date_distributed', '<=', $dateTo);
            }
        }

        // Apply status filter if provided (using confirmed boolean)
        if ($request->has('status') && $request->status) {
            if ($request->status === 'confirmed') {
                $query->where('confirmed', true);
            } elseif ($request->status === 'pending') {
                $query->where('confirmed', false);
            }
        }

        $deliveries = $query->orderBy('date_distributed', 'desc')
            ->paginate(10);

        // Load returns data for each delivery to calculate returnable quantities
        $deliveries->getCollection()->transform(function ($delivery) {
            // Get approved returns for this distribution
            $approvedReturns = \App\Models\ReturnModel::where('distribution_id', $delivery->id)
                ->where('status', 'approved')
                ->sum('quantity');

            // Calculate max returnable quantity (simplified: full quantity minus approved returns)
            $maxReturnable = max(0, $delivery->quantity - $approvedReturns);

            $delivery->approved_returns_quantity = $approvedReturns;
            $delivery->max_returnable_quantity = $maxReturnable;

            return $delivery;
        });

        // Get statistics for the current filter period
        $statsQuery = Distribution::where('store_id', $store->id);

        // Apply same date filters to stats
        if ($request->filled('date_from') || $request->filled('date_to')) {
            if ($dateFrom) {
                $statsQuery->whereDate('date_distributed', '>=', $dateFrom);
            }
            if ($dateTo) {
                $statsQuery->whereDate('date_distributed', '<=', $dateTo);
            }
        } else {
            // Apply time period filter to stats
            $this->applyTimePeriodFilterDate($statsQuery, $request, 'date_distributed');
        }

        $stats = [
            'total' => $statsQuery->count(),
            'pending' => $statsQuery->where('confirmed', false)->count(),
            'confirmed' => $statsQuery->where('confirmed', true)->count(),
            'with_shortage' => $statsQuery->where('confirmed', true)
                ->whereColumn('received_quantity', '<', 'quantity')->count(),
        ];

        // Get filter options
        $statuses = [
            'pending' => 'Belum Dikonfirmasi',
            'confirmed' => 'Dikonfirmasi'
        ];

        return view('user.deliveries', compact('deliveries', 'statuses', 'dateFrom', 'dateTo', 'stats'));
    }

    /**
     * Display the specified delivery
     */
    public function show($id)
    {
        $user = auth()->user();
        $store = $user->store;

        if (!$store) {
            return redirect()->route('user.dashboard')->with('error', 'Anda belum terdaftar di toko manapun.');
        }

        $delivery = Distribution::where('store_id', $store->id)
            ->with(['product', 'store'])
            ->findOrFail($id);

        // Calculate returnable quantity for this delivery (simplified)
        $approvedReturns = \App\Models\ReturnModel::where('distribution_id', $delivery->id)
            ->where('status', 'approved')
            ->sum('quantity');

        $maxReturnable = max(0, $delivery->quantity - $approvedReturns);

        $delivery->approved_returns_quantity = $approvedReturns;
        $delivery->max_returnable_quantity = $maxReturnable;

        return view('user.deliveries.show', compact('delivery'));
    }

    /**
     * Note: Manual confirmation is no longer needed in the simplified system.
     * All distributions are auto-confirmed when created.
     * This method is kept for backward compatibility but will redirect to deliveries list.
     */
    public function confirm(Request $request, $id)
    {
        return redirect()->route('user.deliveries')
            ->with('info', 'Distribusi telah dikonfirmasi secara otomatis. Jika ada masalah, silakan buat permintaan retur.');
    }
}
