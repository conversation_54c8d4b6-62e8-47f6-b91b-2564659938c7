<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Product extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
    ];

    /**
     * Get the warehouse stock for the product.
     */
    public function warehouseStock()
    {
        return $this->hasMany(WarehouseStock::class);
    }

    /**
     * Get the distributions for the product.
     */
    public function distributions()
    {
        return $this->hasMany(Distribution::class);
    }

    /**
     * Get the store stock for the product.
     */
    public function storeStock()
    {
        return $this->hasMany(StoreStock::class);
    }

    /**
     * Get the stock opname for the product.
     */
    public function stockOpname()
    {
        return $this->hasMany(StockOpname::class);
    }

    /**
     * Get the supplier deliveries for the product.
     */
    public function supplierDeliveries()
    {
        return $this->hasMany(SupplierDelivery::class);
    }

    /**
     * Get the most recent supplier who delivered this product.
     */
    public function getMostRecentSupplier()
    {
        return $this->supplierDeliveries()
            ->with('supplier')
            ->where('status', 'received')
            ->orderBy('received_date', 'desc')
            ->first()?->supplier;
    }

    /**
     * Get all suppliers who have delivered this product, ordered by most recent.
     */
    public function getSuppliersWhoDelivered()
    {
        return Supplier::whereHas('supplierDeliveries', function ($query) {
            $query->where('product_id', $this->id)
                  ->where('status', 'received');
        })->withCount(['supplierDeliveries as delivery_count' => function ($query) {
            $query->where('product_id', $this->id)
                  ->where('status', 'received');
        }])->orderBy('delivery_count', 'desc')->get();
    }

    /**
     * Get the returns for the product.
     */
    public function returns()
    {
        return $this->hasMany(ReturnModel::class);
    }

    /**
     * Get current warehouse stock quantity
     * Always queries fresh data from database
     * Only counts central warehouse stock (store_id = null)
     */
    public function getCurrentWarehouseStock()
    {
        // Use fresh query to avoid caching issues
        // Only count central warehouse stock (store_id = null)
        return \App\Models\WarehouseStock::where('product_id', $this->id)
            ->whereNull('store_id')
            ->sum('quantity');
    }

    /**
     * Get pending distributions quantity (unconfirmed)
     * Always queries fresh data from database
     * Note: With auto-acceptance system, this will always return 0
     * since all distributions are auto-confirmed
     */
    public function getPendingDistributionsQuantity()
    {
        // With auto-acceptance system, all distributions are confirmed immediately
        // This method is kept for backward compatibility but will always return 0
        return 0;
    }

    /**
     * Get available stock for new distributions
     * Available = Current warehouse stock (with auto-acceptance, all stock is available)
     */
    public function getAvailableStock()
    {
        return $this->getCurrentWarehouseStock(); // All warehouse stock is available with auto-acceptance
    }

    /**
     * Get committed stock (pending distributions)
     * With auto-acceptance, no stock is committed/pending
     */
    public function getCommittedStock()
    {
        return 0; // No committed stock with auto-acceptance system
    }

    /**
     * Get confirmed distributions count
     * With auto-acceptance, all distributions are confirmed
     */
    public function getConfirmedDistributionsCount()
    {
        return $this->distributions()->count(); // All distributions are auto-confirmed
    }

    /**
     * Get pending distributions count
     * With auto-acceptance, no pending distributions exist
     */
    public function getPendingDistributionsCount()
    {
        return 0; // No pending distributions with auto-acceptance system
    }

    /**
     * Get detailed stock information for display
     * Updated for auto-acceptance system
     */
    public function getStockSummary()
    {
        $warehouseStock = $this->getCurrentWarehouseStock();
        $pendingDistributions = 0; // No pending distributions with auto-acceptance
        $availableStock = $warehouseStock; // All warehouse stock is available
        $storeStock = $this->storeStock()->sum('quantity');
        $confirmedDistributions = $this->distributions()->sum('quantity'); // All distributions are auto-confirmed

        return [
            'warehouse_stock' => $warehouseStock,
            'pending_distributions' => $pendingDistributions,
            'available_stock' => $availableStock,
            'store_stock' => $storeStock,
            'confirmed_distributions' => $confirmedDistributions,
            'total_distributed' => $confirmedDistributions,
            'has_pending_distributions' => $pendingDistributions > 0,
            'can_reduce_stock' => $availableStock > 0,
        ];
    }

    /**
     * Check if stock can be reduced by specified amount
     */
    public function canReduceStock($amount)
    {
        return $this->getAvailableStock() >= $amount;
    }

    /**
     * Check if stock can be set to specified amount
     */
    public function canSetStock($amount)
    {
        return $amount >= $this->getPendingDistributionsQuantity();
    }

    /**
     * Get reasons why product cannot be deleted
     */
    public function getDeletionBlockingReasons()
    {
        $reasons = [];

        if ($this->warehouseStock()->count() > 0) {
            $reasons[] = [
                'type' => 'warehouse_stock',
                'message' => 'Memiliki stok gudang',
                'count' => $this->warehouseStock()->count(),
                'quantity' => $this->getCurrentWarehouseStock()
            ];
        }

        if ($this->distributions()->count() > 0) {
            $reasons[] = [
                'type' => 'distributions',
                'message' => 'Memiliki riwayat distribusi',
                'count' => $this->distributions()->count(),
                'pending' => $this->getPendingDistributionsQuantity()
            ];
        }

        if ($this->storeStock()->count() > 0) {
            $reasons[] = [
                'type' => 'store_stock',
                'message' => 'Memiliki stok di toko',
                'count' => $this->storeStock()->count(),
                'quantity' => $this->storeStock()->sum('quantity')
            ];
        }

        if ($this->stockOpname()->count() > 0) {
            $reasons[] = [
                'type' => 'stock_opname',
                'message' => 'Memiliki riwayat stock opname',
                'count' => $this->stockOpname()->count()
            ];
        }

        return $reasons;
    }

    /**
     * Check if product can be deleted
     */
    public function canBeDeleted()
    {
        return empty($this->getDeletionBlockingReasons());
    }
}
