<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\Distribution;
use App\Models\StoreStock;
use App\Models\Store;
use App\Services\DistributionAnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserDashboardController extends Controller
{
    protected $distributionAnalyticsService;

    public function __construct(DistributionAnalyticsService $distributionAnalyticsService)
    {
        $this->distributionAnalyticsService = $distributionAnalyticsService;
    }

    public function index(Request $request)
    {
        $user = auth()->user();

        // Get dynamic store information based on user's store relationship
        $store = $user->store;
        $storeInfo = [
            'name' => $store ? $store->name : 'Toko Tidak Diketahui',
            'code' => $this->generateStoreCode($store ? $store->name : ''),
            'manager' => $user->name
        ];

        // Get real statistics from database
        $stats = $this->getStoreStatistics($store);

        // Get pending deliveries for this store
        $pendingDeliveries = $this->getPendingDeliveries($store);

        // Get distribution analytics data for this store
        $analytics = $store ? $this->distributionAnalyticsService->getUserAnalytics($store->id) : [
            'summary' => ['total_distributions' => 0, 'total_sent' => 0, 'total_received' => 0, 'total_difference' => 0, 'difference_type' => 'perfect'],
            'recent_distributions' => []
        ];

        // Quick actions aligned with current system
        $quickActions = [
            [
                'title' => 'Stok Toko',
                'description' => 'Lihat stok produk di toko',
                'icon' => 'package',
                'route' => 'user.inventory',
                'color' => 'blue'
            ],
            [
                'title' => 'Kelola Produk',
                'description' => 'Kelola stok produk toko',
                'icon' => 'cog',
                'route' => 'user.products.index',
                'color' => 'purple'
            ],
            [
                'title' => 'Distribusi',
                'description' => 'Lihat distribusi dari gudang',
                'icon' => 'truck',
                'route' => 'user.deliveries',
                'color' => 'orange'
            ],
            [
                'title' => 'Terima Barang',
                'description' => 'Konfirmasi penerimaan barang',
                'icon' => 'check-circle',
                'route' => 'user.deliveries',
                'color' => 'green'
            ],

        ];

        return view('user.dashboard', compact('storeInfo', 'stats', 'pendingDeliveries', 'quickActions', 'analytics'));
    }

    /**
     * Generate store code based on store location
     */
    private function generateStoreCode($storeLocation)
    {
        if (!$storeLocation) {
            return 'TK000';
        }

        // Extract store number from location string
        preg_match('/(\d+)/', $storeLocation, $matches);
        $storeNumber = $matches[0] ?? '000';

        return 'TK' . str_pad($storeNumber, 3, '0', STR_PAD_LEFT);
    }

    /**
     * Get store statistics from database
     */
    private function getStoreStatistics($store)
    {
        if (!$store) {
            return [
                'total_products' => 0,
                'total_stock' => 0,
                'pending_distributions' => 0,
                'low_stock_items' => 0
            ];
        }

        // Get total products in store stock
        $totalProducts = StoreStock::where('store_id', $store->id)->count();

        // Get total stock quantity in store
        $totalStock = StoreStock::where('store_id', $store->id)->sum('quantity');

        // Get pending distributions count
        // With auto-acceptance system, no pending distributions exist
        $pendingDistributions = 0;

        // Get low stock items count (items with quantity <= 5)
        $lowStockItems = StoreStock::where('store_id', $store->id)
            ->where('quantity', '<=', 5)
            ->count();

        return [
            'total_products' => $totalProducts,
            'total_stock' => $totalStock,
            'pending_distributions' => $pendingDistributions,
            'low_stock_items' => $lowStockItems
        ];
    }

    /**
     * Get pending deliveries for the store
     */
    private function getPendingDeliveries($store)
    {
        if (!$store) {
            return [];
        }

        $deliveries = Distribution::where('store_id', $store->id)
            ->where('confirmed', false)
            ->with(['product', 'store'])
            ->orderBy('date_distributed', 'desc')
            ->limit(5)
            ->get();

        return $deliveries->map(function ($delivery) {
            // Determine priority based on distribution date
            $priority = 'low';
            if ($delivery->date_distributed && $delivery->date_distributed->isToday()) {
                $priority = 'high';
            } elseif ($delivery->date_distributed && $delivery->date_distributed->isTomorrow()) {
                $priority = 'medium';
            }

            return [
                'id' => $delivery->id,
                'product_name' => $delivery->product ? $delivery->product->name : 'Produk tidak diketahui',
                'quantity' => $delivery->quantity . ' unit',
                'date_distributed' => $delivery->date_distributed ? $delivery->date_distributed->format('d M Y') : 'Belum dijadwalkan',
                'status' => $delivery->confirmed ? 'Dikonfirmasi' : 'Menunggu Konfirmasi',
                'priority' => $priority
            ];
        })->toArray();
    }
}
