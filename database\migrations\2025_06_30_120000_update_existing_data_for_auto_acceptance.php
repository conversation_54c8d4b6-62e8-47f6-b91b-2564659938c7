<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Update existing distributions to be auto-confirmed
        DB::table('distributions')->update([
            'confirmed' => true,
            'received_quantity' => DB::raw('quantity'),
            'confirmed_at' => DB::raw('COALESCE(confirmed_at, created_at)')
        ]);

        // Update existing supplier deliveries to be auto-received
        DB::table('supplier_deliveries')->update([
            'status' => 'received',
            'received_quantity' => DB::raw('quantity'),
            'received_date' => DB::raw('COALESCE(received_date, delivery_date)')
        ]);

        // Update any pending supplier deliveries to have received_by set to first admin user
        $firstAdmin = DB::table('users')->where('role', 'admin')->first();
        if ($firstAdmin) {
            DB::table('supplier_deliveries')
                ->whereNull('received_by')
                ->update(['received_by' => $firstAdmin->id]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Note: This migration is not easily reversible as we're updating existing data
        // to match the new auto-acceptance behavior. Reverting would require knowing
        // the original state of each record, which we don't store.
        
        // If needed, you could reset all distributions to unconfirmed state:
        // DB::table('distributions')->update([
        //     'confirmed' => false,
        //     'received_quantity' => null,
        //     'confirmed_at' => null
        // ]);
        
        // And reset supplier deliveries to pending:
        // DB::table('supplier_deliveries')->update([
        //     'status' => 'pending',
        //     'received_quantity' => null,
        //     'received_date' => null
        // ]);
    }
};
