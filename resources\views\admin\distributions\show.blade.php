@extends('layouts.admin')

@section('title', 'Detail Distribusi - Indah Berkah Abadi')
@section('page-title', 'Detail Distribusi')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">{{ $distribution->product->name }}</h1>
                    <p class="text-gray-600 mt-1">Distri<PERSON><PERSON> ke {{ $distribution->store->name }}</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    @if(!$distribution->confirmed)
                        <a href="{{ route('admin.distributions.edit', $distribution) }}" class="admin-dashboard-btn admin-dashboard-btn-primary">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Edit Distribusi
                        </a>
                    @endif
                    <a href="{{ route('admin.distributions.index') }}" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali ke Daftar
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Overview -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex items-center justify-center mb-6">
                <span class="inline-flex px-4 py-2 text-lg font-semibold rounded-full {{ $distribution->status_badge_class }}">
                    {{ $distribution->status_label }}
                </span>
            </div>
        </div>
    </div>

    <!-- Operational Data -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Data Operasional</h2>
            <p class="admin-dashboard-card-description">Informasi penting untuk operasional distribusi</p>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Quantity Information -->
                <div class="bg-blue-50 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-blue-900">Informasi Kuantitas</h3>
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-blue-700 font-medium">Jumlah Dikirim</span>
                            <span class="text-2xl font-bold text-blue-900">{{ number_format($distribution->quantity) }}</span>
                        </div>
                        @if($distribution->confirmed && $distribution->received_quantity !== null)
                        <div class="flex justify-between items-center">
                            <span class="text-blue-700 font-medium">Jumlah Diterima</span>
                            <span class="text-2xl font-bold text-green-600">{{ number_format($distribution->received_quantity) }}</span>
                        </div>
                        @php
                            $difference = $distribution->received_quantity - $distribution->quantity;
                        @endphp
                        @if($difference != 0)
                        <div class="flex justify-between items-center pt-2 border-t border-blue-200">
                            <span class="text-blue-700 font-medium">Selisih</span>
                            <span class="text-lg font-bold {{ $difference > 0 ? 'text-green-600' : 'text-red-600' }}">
                                {{ $difference > 0 ? '+' : '' }}{{ number_format($difference) }}
                            </span>
                        </div>
                        @endif
                        @else
                        <div class="text-center py-4">
                            <p class="text-blue-700 text-sm">Belum dikonfirmasi oleh toko</p>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Store Information -->
                <div class="bg-green-50 rounded-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-green-900">Informasi Toko</h3>
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div>
                            <span class="text-green-700 font-medium block">Nama Toko</span>
                            <span class="text-xl font-bold text-green-900">{{ $distribution->store->name }}</span>
                        </div>
                        <div>
                            <span class="text-green-700 font-medium block">Lokasi</span>
                            <span class="text-green-900">{{ $distribution->store->location }}</span>
                        </div>
                        <div>
                            <span class="text-green-700 font-medium block">Tanggal Distribusi</span>
                            <span class="text-green-900">{{ $distribution->date_distributed->format('d F Y') }}</span>
                        </div>
                        @if($distribution->confirmed && $distribution->confirmed_at)
                        <div>
                            <span class="text-green-700 font-medium block">Dikonfirmasi Pada</span>
                            <span class="text-green-900">{{ $distribution->confirmed_at->format('d F Y, H:i') }}</span>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Notes (if confirmed) -->
    @if($distribution->confirmed && $distribution->notes)
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Catatan dari Toko</h2>
            <p class="admin-dashboard-card-description">Catatan yang diberikan saat konfirmasi penerimaan</p>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="bg-gray-50 rounded-lg p-4">
                <p class="text-gray-900 leading-relaxed">{{ $distribution->notes }}</p>
            </div>
        </div>
    </div>
    @endif



    <!-- Auto-Confirmation Status (all distributions are now auto-confirmed) -->
    <div class="admin-dashboard-card bg-green-50 border-green-200">
        <div class="admin-dashboard-card-content">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold text-green-900">Distribusi Auto-Dikonfirmasi</h3>
                    <p class="text-sm text-green-700 mt-1">
                        Distribusi ini telah dikonfirmasi secara otomatis dan stok telah dipindahkan ke toko. Jika ada masalah, gunakan sistem retur.
                    </p>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
